"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Trash2, MoreVertical, Tag } from 'lucide-react';
import { CartItem } from '@/types/cart';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useWishlist } from '@/contexts/WishlistContext';
import QuantityStepper from '@/components/ui/quantity-stepper';
import ConfirmationDialog from '@/components/ui/confirmation-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface MobileCartItemProps {
  item: CartItem;
  onUpdateQuantity: (productId: string, quantity: number) => void;
  onRemove: (productId: string) => void;
  onMoveToWishlist?: (productId: string) => void;
  loading?: boolean;
  className?: string;
}

const MobileCartItem: React.FC<MobileCartItemProps> = ({
  item,
  onUpdateQuantity,
  onRemove,
  onMoveToWishlist,
  loading = false,
  className = '',
}) => {
  const { formatPrice } = useCurrency();
  const { addToWishlist, isInWishlist } = useWishlist();
  const [showActions, setShowActions] = useState(false);
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [quantityLoading, setQuantityLoading] = useState(false);

  // Calculate discounted price
  const getDiscountedPrice = (price: number, discount: string) => {
    return discount ? Math.round(price - (price * parseInt(discount)) / 100) : price;
  };

  const discountedPrice = item.product.discount
    ? getDiscountedPrice(item.product.price, item.product.discount)
    : item.product.price;

  const totalPrice = discountedPrice * item.quantity;
  const savings = item.product.discount
    ? (item.product.price - discountedPrice) * item.quantity
    : 0;

  const handleQuantityChange = async (newQuantity: number) => {
    setQuantityLoading(true);
    try {
      await onUpdateQuantity(item.product.id, newQuantity);
    } catch (error) {
      toast.error('Failed to update quantity');
    } finally {
      setQuantityLoading(false);
    }
  };

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await onRemove(item.product.id);
      setShowRemoveDialog(false);
      toast.success(`${item.product.title} removed from cart`);
    } catch (error) {
      toast.error('Failed to remove item');
      setIsRemoving(false);
    }
  };

  const handleMoveToWishlist = async () => {
    try {
      if (onMoveToWishlist) {
        await onMoveToWishlist(item.product.id);
      } else {
        await addToWishlist(item.product);
        await onRemove(item.product.id);
      }
      toast.success(`${item.product.title} moved to wishlist`);
      setShowActions(false);
    } catch (error) {
      toast.error('Failed to move to wishlist');
    }
  };

  return (
    <>
      <motion.div
        className={cn(
          'bg-white rounded-lg shadow-sm border border-gray-100 overflow-hidden',
          'touch-manipulation', // Optimize for touch
          loading && 'opacity-75 pointer-events-none',
          className
        )}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, x: -100, scale: 0.95 }}
        transition={{ duration: 0.3 }}
      >
        {/* Main Content */}
        <div className="p-4">
          <div className="flex gap-3">
            {/* Product Image */}
            <div className="relative w-20 h-20 bg-gray-50 rounded-lg overflow-hidden flex-shrink-0">
              <Image
                src={item.product.image || '/assets/product_images/placeholder.png'}
                alt={item.product.title}
                fill
                className="object-cover"
                sizes="80px"
              />
              
              {/* Discount Badge */}
              {item.product.discount && (
                <div className="absolute top-1 left-1 bg-red-500 text-white text-xs px-1.5 py-0.5 rounded-full font-medium flex items-center gap-1">
                  <Tag size={8} />
                  {item.product.discount}%
                </div>
              )}
            </div>

            {/* Product Details */}
            <div className="flex-1 min-w-0">
              <div className="flex justify-between items-start mb-2">
                <Link href={`/products/${item.product.id}`}>
                  <h3 className="font-semibold text-gray-900 text-sm line-clamp-2 pr-2">
                    {item.product.title}
                  </h3>
                </Link>
                
                {/* Actions Menu */}
                <button
                  onClick={() => setShowActions(!showActions)}
                  className="p-1 text-gray-400 hover:text-gray-600 touch-target"
                >
                  <MoreVertical size={16} />
                </button>
              </div>

              <p className="text-xs text-gray-500 mb-2">{item.product.category}</p>

              {/* Price */}
              <div className="flex items-center gap-2 mb-3">
                <span className="font-bold text-accent">
                  {formatPrice(discountedPrice)}
                </span>
                {item.product.discount && (
                  <>
                    <span className="text-xs text-gray-400 line-through">
                      {formatPrice(item.product.price)}
                    </span>
                    <span className="text-xs text-green-600 font-medium">
                      Save {formatPrice(item.product.price - discountedPrice)}
                    </span>
                  </>
                )}
              </div>

              {/* Quantity and Total */}
              <div className="flex items-center justify-between">
                <QuantityStepper
                  value={item.quantity}
                  onChange={handleQuantityChange}
                  loading={quantityLoading}
                  disabled={loading}
                  size="sm"
                  min={1}
                  max={99}
                />
                
                <div className="text-right">
                  <div className="font-bold text-gray-900">
                    {formatPrice(totalPrice)}
                  </div>
                  {savings > 0 && (
                    <div className="text-xs text-green-600">
                      Save {formatPrice(savings)}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Expandable Actions */}
        <AnimatePresence>
          {showActions && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="border-t border-gray-100 bg-gray-50 overflow-hidden"
            >
              <div className="p-3 flex gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMoveToWishlist}
                  disabled={loading || isInWishlist(item.product.id)}
                  className="flex-1 text-gray-600 hover:text-accent touch-target"
                >
                  <Heart size={16} className={isInWishlist(item.product.id) ? 'fill-current' : ''} />
                  <span className="ml-2">
                    {isInWishlist(item.product.id) ? 'Saved' : 'Save for Later'}
                  </span>
                </Button>

                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRemoveDialog(true)}
                  disabled={loading}
                  className="flex-1 text-gray-600 hover:text-red-500 touch-target"
                >
                  <Trash2 size={16} />
                  <span className="ml-2">Remove</span>
                </Button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Remove Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showRemoveDialog}
        onClose={() => setShowRemoveDialog(false)}
        onConfirm={handleRemove}
        title="Remove Item"
        description={`Are you sure you want to remove "${item.product.title}" from your cart?`}
        confirmText="Remove"
        cancelText="Keep"
        variant="danger"
        loading={isRemoving}
      />
    </>
  );
};

export default MobileCartItem;
