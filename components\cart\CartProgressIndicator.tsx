"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { ShoppingCart, CreditCard, CheckCircle, Package } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Step {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  href?: string;
}

interface CartProgressIndicatorProps {
  currentStep: 'cart' | 'checkout' | 'payment' | 'confirmation';
  className?: string;
  showLabels?: boolean;
  variant?: 'horizontal' | 'vertical';
}

const CartProgressIndicator: React.FC<CartProgressIndicatorProps> = ({
  currentStep,
  className = '',
  showLabels = true,
  variant = 'horizontal',
}) => {
  const steps: Step[] = [
    {
      id: 'cart',
      title: 'Shopping Cart',
      description: 'Review your items',
      icon: <ShoppingCart size={20} />,
      href: '/cart',
    },
    {
      id: 'checkout',
      title: 'Checkout',
      description: 'Enter your details',
      icon: <CreditCard size={20} />,
      href: '/checkout',
    },
    {
      id: 'payment',
      title: 'Payment',
      description: 'Complete your order',
      icon: <Package size={20} />,
    },
    {
      id: 'confirmation',
      title: 'Confirmation',
      description: 'Order complete',
      icon: <CheckCircle size={20} />,
    },
  ];

  const currentStepIndex = steps.findIndex(step => step.id === currentStep);

  const getStepStatus = (stepIndex: number) => {
    if (stepIndex < currentStepIndex) return 'completed';
    if (stepIndex === currentStepIndex) return 'current';
    return 'upcoming';
  };

  const stepVariants = {
    completed: {
      backgroundColor: 'var(--accent)',
      color: 'white',
      scale: 1,
    },
    current: {
      backgroundColor: 'var(--accent)',
      color: 'white',
      scale: 1.1,
    },
    upcoming: {
      backgroundColor: '#f3f4f6',
      color: '#6b7280',
      scale: 1,
    },
  };

  const lineVariants = {
    completed: {
      backgroundColor: 'var(--accent)',
      scaleX: 1,
    },
    upcoming: {
      backgroundColor: '#e5e7eb',
      scaleX: 1,
    },
  };

  if (variant === 'vertical') {
    return (
      <div className={cn('flex flex-col space-y-4', className)}>
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isLast = index === steps.length - 1;

          return (
            <div key={step.id} className="flex items-start space-x-4">
              {/* Step Circle */}
              <div className="relative flex-shrink-0">
                <motion.div
                  className="w-10 h-10 rounded-full flex items-center justify-center border-2"
                  variants={stepVariants}
                  animate={status}
                  transition={{ duration: 0.3 }}
                  style={{
                    borderColor: status === 'upcoming' ? '#e5e7eb' : 'var(--accent)',
                  }}
                >
                  {step.icon}
                </motion.div>

                {/* Connecting Line */}
                {!isLast && (
                  <motion.div
                    className="absolute top-10 left-1/2 w-0.5 h-8 -translate-x-1/2"
                    variants={lineVariants}
                    animate={status === 'completed' ? 'completed' : 'upcoming'}
                    transition={{ duration: 0.3 }}
                  />
                )}
              </div>

              {/* Step Content */}
              {showLabels && (
                <div className="flex-1 min-w-0">
                  <h3
                    className={cn(
                      'text-sm font-medium',
                      status === 'current' ? 'text-accent' : 
                      status === 'completed' ? 'text-gray-900' : 'text-gray-500'
                    )}
                  >
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-500 mt-1">
                    {step.description}
                  </p>
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  }

  return (
    <div className={cn('flex items-center justify-between w-full', className)}>
      {steps.map((step, index) => {
        const status = getStepStatus(index);
        const isLast = index === steps.length - 1;

        return (
          <React.Fragment key={step.id}>
            {/* Step */}
            <div className="flex flex-col items-center space-y-2">
              {/* Step Circle */}
              <motion.div
                className="w-10 h-10 rounded-full flex items-center justify-center border-2 relative"
                variants={stepVariants}
                animate={status}
                transition={{ duration: 0.3 }}
                style={{
                  borderColor: status === 'upcoming' ? '#e5e7eb' : 'var(--accent)',
                }}
              >
                {step.icon}
                
                {/* Pulse Animation for Current Step */}
                {status === 'current' && (
                  <motion.div
                    className="absolute inset-0 rounded-full border-2 border-accent"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [1, 0, 1],
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      ease: 'easeInOut',
                    }}
                  />
                )}
              </motion.div>

              {/* Step Labels */}
              {showLabels && (
                <div className="text-center">
                  <h3
                    className={cn(
                      'text-xs font-medium',
                      status === 'current' ? 'text-accent' : 
                      status === 'completed' ? 'text-gray-900' : 'text-gray-500'
                    )}
                  >
                    {step.title}
                  </h3>
                  <p className="text-xs text-gray-400 mt-1 hidden sm:block">
                    {step.description}
                  </p>
                </div>
              )}
            </div>

            {/* Connecting Line */}
            {!isLast && (
              <div className="flex-1 px-2">
                <div className="relative h-0.5 bg-gray-200 rounded-full overflow-hidden">
                  <motion.div
                    className="absolute inset-y-0 left-0 h-full rounded-full"
                    variants={lineVariants}
                    animate={status === 'completed' ? 'completed' : 'upcoming'}
                    transition={{ duration: 0.5, delay: 0.1 }}
                    style={{ originX: 0 }}
                  />
                </div>
              </div>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default CartProgressIndicator;
