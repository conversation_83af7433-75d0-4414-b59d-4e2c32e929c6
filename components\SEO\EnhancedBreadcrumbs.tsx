"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { ChevronRight, Home } from "lucide-react";
import Script from "next/script";
import { getUrl } from "@/config/site";

interface BreadcrumbItem {
  name: string;
  href: string;
  current?: boolean;
}

interface EnhancedBreadcrumbsProps {
  customItems?: BreadcrumbItem[];
  showHome?: boolean;
  showStructuredData?: boolean;
  className?: string;
  maxItems?: number;
}

/**
 * Enhanced Breadcrumbs Component with SEO optimization
 * Includes structured data, accessibility features, and customization options
 */
export default function EnhancedBreadcrumbs({
  customItems,
  showHome = true,
  showStructuredData = true,
  className = "",
  maxItems = 5,
}: EnhancedBreadcrumbsProps) {
  const pathname = usePathname();

  // Generate breadcrumb items from pathname
  const generateBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) return customItems;

    const pathSegments = pathname.split("/").filter(Boolean);
    const breadcrumbs: BreadcrumbItem[] = [];

    // Add home if enabled
    if (showHome) {
      breadcrumbs.push({
        name: "Home",
        href: "/",
        current: pathname === "/",
      });
    }

    // Generate breadcrumbs from path segments
    let currentPath = "";
    pathSegments.forEach((segment, index) => {
      currentPath += `/${segment}`;
      const isLast = index === pathSegments.length - 1;
      
      // Format segment name
      let name = segment
        .replace(/-/g, " ")
        .replace(/\b\w/g, (l) => l.toUpperCase());

      // Custom naming for specific routes
      const routeNames: { [key: string]: string } = {
        "about-us": "About Us",
        "chiniot-furniture": "Chiniot Furniture",
        "video-blogs": "Video Blogs",
        "custom-orders": "Custom Orders",
        "terms-of-service": "Terms of Service",
        "privacy-policy": "Privacy Policy",
      };

      if (routeNames[segment]) {
        name = routeNames[segment];
      }

      breadcrumbs.push({
        name,
        href: currentPath,
        current: isLast,
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  // Limit breadcrumbs if maxItems is set
  const displayBreadcrumbs = breadcrumbs.length > maxItems
    ? [
        breadcrumbs[0], // Home
        { name: "...", href: "#", current: false },
        ...breadcrumbs.slice(-2), // Last 2 items
      ]
    : breadcrumbs;

  // Generate structured data for breadcrumbs
  const breadcrumbStructuredData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.href === "#" ? undefined : getUrl(item.href),
    })),
  };

  // Don't render breadcrumbs on home page unless there are custom items
  if (pathname === "/" && !customItems) {
    return null;
  }



  return (
    <>
      {/* Structured Data */}
      {showStructuredData && (
        <Script
          id="breadcrumb-structured-data"
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify(breadcrumbStructuredData),
          }}
        />
      )}

      {/* Breadcrumb Navigation */}
      <nav
        className={`w-full overflow-hidden ${className}`}
        aria-label="Breadcrumb navigation"
        role="navigation"
      >
        {/* Mobile breadcrumbs - show only last 2 items on very small screens */}
        <div className="block sm:hidden">
          <ol className="flex items-center space-x-1 text-xs text-gray-600" itemScope itemType="https://schema.org/BreadcrumbList">
            {displayBreadcrumbs.length > 2 ? (
              <>
                {/* Show home icon */}
                <li className="flex items-center" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
                  <Link
                    href="/"
                    className="text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm p-1"
                    itemProp="item"
                  >
                    <Home className="h-3 w-3" aria-label="Home" />
                  </Link>
                  <meta itemProp="position" content="1" />
                </li>

                {/* Separator */}
                <li className="flex items-center text-gray-400">
                  <ChevronRight className="h-3 w-3" />
                </li>

                {/* Ellipsis */}
                <li className="flex items-center">
                  <span className="text-gray-400 text-xs">...</span>
                </li>

                {/* Separator */}
                <li className="flex items-center text-gray-400">
                  <ChevronRight className="h-3 w-3" />
                </li>

                {/* Last item */}
                <li className="flex items-center min-w-0 flex-1" itemProp="itemListElement" itemScope itemType="https://schema.org/ListItem">
                  <span
                    className="font-medium text-gray-900 cursor-default truncate text-xs"
                    aria-current="page"
                    itemProp="name"
                    title={displayBreadcrumbs[displayBreadcrumbs.length - 1].name}
                  >
                    {displayBreadcrumbs[displayBreadcrumbs.length - 1].name}
                  </span>
                  <meta itemProp="position" content={String(displayBreadcrumbs.length)} />
                </li>
              </>
            ) : (
              displayBreadcrumbs.map((item, index) => (
                <li
                  key={`mobile-${item.href}-${index}`}
                  className="flex items-center min-w-0"
                  itemProp="itemListElement"
                  itemScope
                  itemType="https://schema.org/ListItem"
                >
                  {index > 0 && (
                    <ChevronRight className="h-3 w-3 text-gray-400 mx-1 flex-shrink-0" />
                  )}

                  {item.current ? (
                    <span
                      className="font-medium text-gray-900 cursor-default truncate text-xs"
                      aria-current="page"
                      itemProp="name"
                      title={item.name}
                    >
                      {item.name === "Home" && showHome ? (
                        <Home className="h-3 w-3" aria-label="Home" />
                      ) : (
                        item.name
                      )}
                    </span>
                  ) : item.href === "#" ? (
                    <span className="text-gray-400 text-xs truncate" aria-hidden="true" title={item.name}>
                      {item.name}
                    </span>
                  ) : (
                    <Link
                      href={item.href}
                      className="text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm px-1 py-0.5 truncate text-xs"
                      itemProp="item"
                      title={item.name}
                    >
                      <span itemProp="name">
                        {item.name === "Home" && showHome ? (
                          <Home className="h-3 w-3" aria-label="Home" />
                        ) : (
                          item.name
                        )}
                      </span>
                    </Link>
                  )}

                  <meta itemProp="position" content={String(index + 1)} />
                </li>
              ))
            )}
          </ol>
        </div>

        {/* Tablet and Desktop breadcrumbs */}
        <div className="hidden sm:block">
          <ol className="flex items-center flex-wrap gap-x-1 gap-y-1 text-sm text-gray-600" itemScope itemType="https://schema.org/BreadcrumbList">
            {displayBreadcrumbs.map((item, index) => (
              <li
                key={`desktop-${item.href}-${index}`}
                className="flex items-center"
                itemProp="itemListElement"
                itemScope
                itemType="https://schema.org/ListItem"
              >
                {/* Add separator before each item except the first */}
                {index > 0 && (
                  <ChevronRight className="h-4 w-4 text-gray-400 mx-1 flex-shrink-0" />
                )}

                {item.current ? (
                  <span
                    className="font-medium text-gray-900 cursor-default break-words"
                    aria-current="page"
                    itemProp="name"
                  >
                    {item.name === "Home" && showHome ? (
                      <Home className="h-4 w-4" aria-label="Home" />
                    ) : (
                      item.name
                    )}
                  </span>
                ) : item.href === "#" ? (
                  <span className="text-gray-400 break-words" aria-hidden="true">
                    {item.name}
                  </span>
                ) : (
                  <Link
                    href={item.href}
                    className="text-gray-500 hover:text-gray-700 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-sm px-1 py-0.5 break-words"
                    itemProp="item"
                  >
                    <span itemProp="name">
                      {item.name === "Home" && showHome ? (
                        <Home className="h-4 w-4" aria-label="Home" />
                      ) : (
                        item.name
                      )}
                    </span>
                  </Link>
                )}

                {/* Hidden structured data */}
                <meta itemProp="position" content={String(index + 1)} />
              </li>
            ))}
          </ol>
        </div>
      </nav>
    </>
  );
}

// Utility function to generate breadcrumbs for specific pages
export const generatePageBreadcrumbs = (
  pageName: string,
  parentPages?: { name: string; href: string }[]
): BreadcrumbItem[] => {
  const breadcrumbs: BreadcrumbItem[] = [
    { name: "Home", href: "/" },
  ];

  if (parentPages) {
    breadcrumbs.push(...parentPages);
  }

  breadcrumbs.push({
    name: pageName,
    href: "#",
    current: true,
  });

  return breadcrumbs;
};

// Predefined breadcrumbs for common pages
export const commonBreadcrumbs = {
  products: [
    { name: "Home", href: "/" },
    { name: "Products", href: "/products", current: true },
  ],
  aboutUs: [
    { name: "Home", href: "/" },
    { name: "About Us", href: "/about-us", current: true },
  ],
  contact: [
    { name: "Home", href: "/" },
    { name: "Contact", href: "/contact", current: true },
  ],
  chiniotFurniture: [
    { name: "Home", href: "/" },
    { name: "Chiniot Furniture", href: "/chiniot-furniture", current: true },
  ],
  videoBlogs: [
    { name: "Home", href: "/" },
    { name: "Video Blogs", href: "/video-blogs", current: true },
  ],
  cart: [
    { name: "Home", href: "/" },
    { name: "Shopping Cart", href: "/cart", current: true },
  ],
  checkout: [
    { name: "Home", href: "/" },
    { name: "Shopping Cart", href: "/cart" },
    { name: "Checkout", href: "/checkout", current: true },
  ],
  wishlist: [
    { name: "Home", href: "/" },
    { name: "Wishlist", href: "/wishlist", current: true },
  ],
  orders: [
    { name: "Home", href: "/" },
    { name: "My Orders", href: "/orders", current: true },
  ],
  profile: [
    { name: "Home", href: "/" },
    { name: "My Profile", href: "/profile", current: true },
  ],
};

// Hook for using breadcrumbs in components
export const useBreadcrumbs = (customItems?: BreadcrumbItem[]) => {
  const pathname = usePathname();
  
  const getBreadcrumbs = (): BreadcrumbItem[] => {
    if (customItems) return customItems;
    
    // Return predefined breadcrumbs based on pathname
    switch (pathname) {
      case "/products":
        return commonBreadcrumbs.products;
      case "/about-us":
        return commonBreadcrumbs.aboutUs;
      case "/contact":
        return commonBreadcrumbs.contact;
      case "/chiniot-furniture":
        return commonBreadcrumbs.chiniotFurniture;
      case "/video-blogs":
        return commonBreadcrumbs.videoBlogs;
      case "/cart":
        return commonBreadcrumbs.cart;
      case "/checkout":
        return commonBreadcrumbs.checkout;
      case "/wishlist":
        return commonBreadcrumbs.wishlist;
      case "/orders":
        return commonBreadcrumbs.orders;
      case "/profile":
        return commonBreadcrumbs.profile;
      default:
        // Generate from pathname
        const pathSegments = pathname.split("/").filter(Boolean);
        const breadcrumbs: BreadcrumbItem[] = [
          { name: "Home", href: "/" },
        ];

        let currentPath = "";
        pathSegments.forEach((segment, index) => {
          currentPath += `/${segment}`;
          const isLast = index === pathSegments.length - 1;
          
          breadcrumbs.push({
            name: segment.replace(/-/g, " ").replace(/\b\w/g, (l) => l.toUpperCase()),
            href: currentPath,
            current: isLast,
          });
        });

        return breadcrumbs;
    }
  };

  return getBreadcrumbs();
};
