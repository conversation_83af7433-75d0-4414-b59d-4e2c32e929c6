"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ChevronUp, ChevronDown, ShoppingBag } from 'lucide-react';
import { Cart } from '@/types/cart';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';

interface MobileCartLayoutProps {
  cart: Cart;
  children: React.ReactNode;
  summaryComponent: React.ReactNode;
  className?: string;
}

const MobileCartLayout: React.FC<MobileCartLayoutProps> = ({
  cart,
  children,
  summaryComponent,
  className = '',
}) => {
  const [showSummary, setShowSummary] = useState(false);

  return (
    <div className={cn('lg:hidden', className)}>
      {/* Mobile Cart Items */}
      <div className="space-y-4 mb-4">
        {children}
      </div>

      {/* Mobile Summary Toggle */}
      <div className="sticky bottom-0 bg-white border-t border-gray-200 shadow-lg">
        {/* Summary Preview */}
        <button
          onClick={() => setShowSummary(!showSummary)}
          className="w-full p-4 flex items-center justify-between text-left"
        >
          <div className="flex items-center gap-3">
            <ShoppingBag size={20} className="text-accent" />
            <div>
              <div className="font-semibold text-gray-900">
                {cart.totalItems} item{cart.totalItems !== 1 ? 's' : ''}
              </div>
              <div className="text-sm text-gray-600">
                Tap to {showSummary ? 'hide' : 'view'} summary
              </div>
            </div>
          </div>
          
          <div className="flex items-center gap-3">
            <div className="text-right">
              <div className="font-bold text-lg text-accent">
                ${cart.total.toFixed(2)}
              </div>
              {cart.discount > 0 && (
                <div className="text-sm text-green-600">
                  Save ${cart.discount.toFixed(2)}
                </div>
              )}
            </div>
            <motion.div
              animate={{ rotate: showSummary ? 180 : 0 }}
              transition={{ duration: 0.2 }}
            >
              <ChevronUp size={20} className="text-gray-400" />
            </motion.div>
          </div>
        </button>

        {/* Expandable Summary */}
        <AnimatePresence>
          {showSummary && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="overflow-hidden border-t border-gray-100"
            >
              <div className="p-4">
                {summaryComponent}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default MobileCartLayout;
