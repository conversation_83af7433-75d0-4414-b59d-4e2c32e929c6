"use client";

import React, { useEffect, useState } from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { ShoppingBag, ArrowRight, Heart, Clock, Sparkles } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ProductData } from '@/types';
import ProductCard from '@/components/ProductCard';
import { cn } from '@/lib/utils';

interface EnhancedEmptyCartProps {
  recentlyViewed?: ProductData[];
  recommendedProducts?: ProductData[];
  className?: string;
}

const EnhancedEmptyCart: React.FC<EnhancedEmptyCartProps> = ({
  recentlyViewed = [],
  recommendedProducts = [],
  className = '',
}) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="flex flex-col items-center justify-center py-16 text-center">
        <div className="w-16 h-16 bg-gray-200 rounded-full animate-pulse mb-4" />
        <div className="h-6 bg-gray-200 rounded w-48 mb-2 animate-pulse" />
        <div className="h-4 bg-gray-200 rounded w-64 mb-6 animate-pulse" />
        <div className="h-10 bg-gray-200 rounded w-32 animate-pulse" />
      </div>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5, ease: 'easeOut' },
    },
  };

  return (
    <motion.div
      className={cn('max-w-4xl mx-auto', className)}
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Main Empty State */}
      <motion.div
        className="flex flex-col items-center justify-center py-16 text-center"
        variants={itemVariants}
      >
        {/* Animated Shopping Bag Icon */}
        <motion.div
          className="relative mb-6"
          initial={{ scale: 0.8 }}
          animate={{ scale: 1 }}
          transition={{ duration: 0.5, ease: 'easeOut' }}
        >
          <motion.div
            className="w-20 h-20 bg-gradient-to-br from-accent/20 to-accent/10 rounded-full flex items-center justify-center"
            animate={{
              boxShadow: [
                '0 0 0 0 rgba(160, 82, 45, 0.4)',
                '0 0 0 20px rgba(160, 82, 45, 0)',
              ],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatType: 'loop',
            }}
          >
            <ShoppingBag size={40} className="text-accent" />
          </motion.div>
          
          {/* Floating sparkles */}
          <motion.div
            className="absolute -top-2 -right-2"
            animate={{
              rotate: [0, 360],
              scale: [1, 1.2, 1],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: 'easeInOut',
            }}
          >
            <Sparkles size={16} className="text-yellow-500" />
          </motion.div>
        </motion.div>

        {/* Title and Description */}
        <motion.h2
          className="text-2xl font-bold text-gray-900 mb-3"
          variants={itemVariants}
        >
          Your cart is empty
        </motion.h2>
        
        <motion.p
          className="text-gray-600 mb-8 max-w-md leading-relaxed"
          variants={itemVariants}
        >
          Looks like you haven't added any products to your cart yet. 
          Discover our amazing collection of handcrafted wooden art pieces.
        </motion.p>

        {/* Action Buttons */}
        <motion.div
          className="flex flex-col sm:flex-row gap-4"
          variants={itemVariants}
        >
          <Link href="/products">
            <Button size="lg" className="group">
              <ShoppingBag size={18} className="mr-2" />
              Start Shopping
              <ArrowRight 
                size={16} 
                className="ml-2 transition-transform group-hover:translate-x-1" 
              />
            </Button>
          </Link>
          
          <Link href="/wishlist">
            <Button variant="outline" size="lg" className="group">
              <Heart size={18} className="mr-2" />
              View Wishlist
              <ArrowRight 
                size={16} 
                className="ml-2 transition-transform group-hover:translate-x-1" 
              />
            </Button>
          </Link>
        </motion.div>
      </motion.div>

      {/* Recently Viewed Products */}
      {recentlyViewed.length > 0 && (
        <motion.section
          className="mb-12"
          variants={itemVariants}
        >
          <div className="flex items-center gap-2 mb-6">
            <Clock size={20} className="text-accent" />
            <h3 className="text-xl font-semibold text-gray-900">
              Recently Viewed
            </h3>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {recentlyViewed.slice(0, 4).map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
          
          {recentlyViewed.length > 4 && (
            <div className="text-center mt-6">
              <Link href="/products">
                <Button variant="ghost" className="group">
                  View All Recently Viewed
                  <ArrowRight 
                    size={16} 
                    className="ml-2 transition-transform group-hover:translate-x-1" 
                  />
                </Button>
              </Link>
            </div>
          )}
        </motion.section>
      )}

      {/* Recommended Products */}
      {recommendedProducts.length > 0 && (
        <motion.section
          variants={itemVariants}
        >
          <div className="flex items-center gap-2 mb-6">
            <Sparkles size={20} className="text-accent" />
            <h3 className="text-xl font-semibold text-gray-900">
              You Might Like
            </h3>
          </div>
          
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {recommendedProducts.slice(0, 4).map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 * index }}
              >
                <ProductCard product={product} />
              </motion.div>
            ))}
          </div>
          
          {recommendedProducts.length > 4 && (
            <div className="text-center mt-6">
              <Link href="/products">
                <Button variant="ghost" className="group">
                  View All Recommendations
                  <ArrowRight 
                    size={16} 
                    className="ml-2 transition-transform group-hover:translate-x-1" 
                  />
                </Button>
              </Link>
            </div>
          )}
        </motion.section>
      )}

      {/* Call to Action Banner */}
      <motion.div
        className="bg-gradient-to-r from-accent/10 to-accent/5 rounded-lg p-8 text-center mt-12"
        variants={itemVariants}
      >
        <h4 className="text-lg font-semibold text-gray-900 mb-2">
          Need Help Finding Something?
        </h4>
        <p className="text-gray-600 mb-4">
          Our collection features unique handcrafted wooden art pieces perfect for your home.
        </p>
        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <Link href="/products?category=featured">
            <Button variant="outline">
              Browse Featured Items
            </Button>
          </Link>
          <Link href="/contact">
            <Button variant="ghost">
              Contact Us for Help
            </Button>
          </Link>
        </div>
      </motion.div>
    </motion.div>
  );
};

export default EnhancedEmptyCart;
