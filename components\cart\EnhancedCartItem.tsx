"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { motion, AnimatePresence } from 'framer-motion';
import { Heart, Trash2, ExternalLink, Tag, Loader2 } from 'lucide-react';
import { CartItem } from '@/types/cart';
import { useCurrency } from '@/contexts/CurrencyContext';
import { useWishlist } from '@/contexts/WishlistContext';
import QuantityStepper from '@/components/ui/quantity-stepper';
import ConfirmationDialog from '@/components/ui/confirmation-dialog';
import { Button } from '@/components/ui/button';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface EnhancedCartItemProps {
  item: CartItem;
  onUpdateQuantity: (productId: string, quantity: number) => void;
  onRemove: (productId: string) => void;
  onMoveToWishlist?: (productId: string) => void;
  loading?: boolean;
  className?: string;
}

const EnhancedCartItem: React.FC<EnhancedCartItemProps> = ({
  item,
  onUpdateQuantity,
  onRemove,
  onMoveToWishlist,
  loading = false,
  className = '',
}) => {
  const { formatPrice } = useCurrency();
  const { addToWishlist, isInWishlist } = useWishlist();
  const [showRemoveDialog, setShowRemoveDialog] = useState(false);
  const [isRemoving, setIsRemoving] = useState(false);
  const [quantityLoading, setQuantityLoading] = useState(false);

  // Calculate discounted price
  const getDiscountedPrice = (price: number, discount: string) => {
    return discount ? Math.round(price - (price * parseInt(discount)) / 100) : price;
  };

  const discountedPrice = item.product.discount
    ? getDiscountedPrice(item.product.price, item.product.discount)
    : item.product.price;

  const totalPrice = discountedPrice * item.quantity;
  const savings = item.product.discount
    ? (item.product.price - discountedPrice) * item.quantity
    : 0;

  const handleQuantityChange = async (newQuantity: number) => {
    setQuantityLoading(true);
    try {
      await onUpdateQuantity(item.product.id, newQuantity);
    } catch (error) {
      toast.error('Failed to update quantity');
    } finally {
      setQuantityLoading(false);
    }
  };

  const handleRemove = async () => {
    setIsRemoving(true);
    try {
      await onRemove(item.product.id);
      setShowRemoveDialog(false);
      toast.success(`${item.product.title} removed from cart`);
    } catch (error) {
      toast.error('Failed to remove item');
      setIsRemoving(false);
    }
  };

  const handleMoveToWishlist = async () => {
    try {
      if (onMoveToWishlist) {
        await onMoveToWishlist(item.product.id);
      } else {
        await addToWishlist(item.product);
        await onRemove(item.product.id);
      }
      toast.success(`${item.product.title} moved to wishlist`);
    } catch (error) {
      toast.error('Failed to move to wishlist');
    }
  };

  return (
    <>
      <motion.div
        className={cn(
          'bg-white border border-gray-100 rounded-lg p-4 shadow-sm',
          'hover:shadow-md transition-shadow duration-200',
          loading && 'opacity-75 pointer-events-none',
          className
        )}
        layout
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, x: -100, scale: 0.95 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex flex-col sm:flex-row gap-4">
          {/* Product Image */}
          <div className="relative w-full sm:w-24 h-24 bg-gray-50 rounded-lg overflow-hidden flex-shrink-0 group">
            <Image
              src={item.product.image || '/assets/product_images/placeholder.png'}
              alt={item.product.title}
              fill
              className="object-cover transition-transform duration-200 group-hover:scale-105"
              sizes="(max-width: 640px) 100vw, 96px"
            />
            
            {/* Discount Badge */}
            {item.product.discount && (
              <div className="absolute top-2 left-2 bg-red-500 text-white text-xs px-2 py-1 rounded-full font-medium flex items-center gap-1">
                <Tag size={10} />
                {item.product.discount}% OFF
              </div>
            )}

            {/* Quick View Overlay */}
            <Link
              href={`/products/${item.product.id}`}
              className="absolute inset-0 bg-black/0 hover:bg-black/20 transition-colors duration-200 flex items-center justify-center opacity-0 hover:opacity-100"
            >
              <ExternalLink size={16} className="text-white" />
            </Link>
          </div>

          {/* Product Details */}
          <div className="flex-grow space-y-3">
            {/* Title and Category */}
            <div>
              <Link href={`/products/${item.product.id}`}>
                <h3 className="font-semibold text-gray-900 hover:text-accent transition-colors line-clamp-2">
                  {item.product.title}
                </h3>
              </Link>
              <p className="text-sm text-gray-500 mt-1">{item.product.category}</p>
            </div>

            {/* Price Information */}
            <div className="flex flex-wrap items-center gap-2">
              <span className="text-lg font-bold text-accent">
                {formatPrice(discountedPrice)}
              </span>
              {item.product.discount && (
                <>
                  <span className="text-sm text-gray-400 line-through">
                    {formatPrice(item.product.price)}
                  </span>
                  <span className="text-sm text-green-600 font-medium">
                    Save {formatPrice(item.product.price - discountedPrice)}
                  </span>
                </>
              )}
            </div>

            {/* Quantity and Actions */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
              {/* Quantity Controls */}
              <div className="flex items-center gap-3">
                <span className="text-sm text-gray-600 font-medium">Qty:</span>
                <QuantityStepper
                  value={item.quantity}
                  onChange={handleQuantityChange}
                  loading={quantityLoading}
                  disabled={loading}
                  size="sm"
                  min={1}
                  max={99}
                />
              </div>

              {/* Action Buttons */}
              <div className="flex items-center gap-2">
                {/* Move to Wishlist */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleMoveToWishlist}
                  disabled={loading || isInWishlist(item.product.id)}
                  className="text-gray-600 hover:text-white"
                >
                  <Heart size={16} className={isInWishlist(item.product.id) ? 'fill-current' : ''} />
                  <span className="hidden sm:inline ml-1">
                    {isInWishlist(item.product.id) ? 'Saved' : 'Save'}
                  </span>
                </Button>

                {/* Remove Button */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowRemoveDialog(true)}
                  disabled={loading}
                  className="text-gray-600 hover:text-white"
                >
                  <Trash2 size={16} />
                  <span className="hidden sm:inline ml-1">Remove</span>
                </Button>
              </div>
            </div>

            {/* Total Price and Savings */}
            <div className="flex justify-between items-center pt-2 border-t border-gray-100">
              <div className="text-sm text-gray-600">
                {item.quantity} × {formatPrice(discountedPrice)}
              </div>
              <div className="text-right">
                <div className="font-bold text-gray-900">
                  {formatPrice(totalPrice)}
                </div>
                {savings > 0 && (
                  <div className="text-sm text-green-600">
                    You save {formatPrice(savings)}
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Loading Overlay */}
        <AnimatePresence>
          {loading && (
            <motion.div
              className="absolute inset-0 bg-white/80 flex items-center justify-center rounded-lg"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Loader2 size={24} className="animate-spin text-accent" />
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Remove Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={showRemoveDialog}
        onClose={() => setShowRemoveDialog(false)}
        onConfirm={handleRemove}
        title="Remove Item"
        description={`Are you sure you want to remove "${item.product.title}" from your cart?`}
        confirmText="Remove"
        cancelText="Keep"
        variant="danger"
        loading={isRemoving}
      />
    </>
  );
};

export default EnhancedCartItem;
