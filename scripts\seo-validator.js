#!/usr/bin/env node

/**
 * SEO Validator <PERSON> for Chinioti Wooden Art
 * Validates SEO implementation across the website
 */

const fs = require('fs');
const path = require('path');

// ANSI color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

// SEO validation results
let validationResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  issues: []
};

// Helper functions
function log(message, color = colors.reset) {
  console.log(`${color}${message}${colors.reset}`);
}

function logSuccess(message) {
  log(`✅ ${message}`, colors.green);
  validationResults.passed++;
}

function logError(message) {
  log(`❌ ${message}`, colors.red);
  validationResults.failed++;
  validationResults.issues.push({ type: 'error', message });
}

function logWarning(message) {
  log(`⚠️  ${message}`, colors.yellow);
  validationResults.warnings++;
  validationResults.issues.push({ type: 'warning', message });
}

function logInfo(message) {
  log(`ℹ️  ${message}`, colors.blue);
}

// Validation functions
function validateSiteConfig() {
  log('\n📋 Validating Site Configuration...', colors.cyan);
  
  try {
    const siteConfigPath = path.join(process.cwd(), 'config', 'site.ts');
    const siteConfigContent = fs.readFileSync(siteConfigPath, 'utf8');
    
    // Check for required fields
    const requiredFields = [
      'name', 'description', 'contact.email', 'contact.phone',
      'seo.keywords', 'seo.location.latitude', 'seo.location.longitude'
    ];
    
    requiredFields.forEach(field => {
      if (siteConfigContent.includes(field)) {
        logSuccess(`Site config contains ${field}`);
      } else {
        logError(`Site config missing ${field}`);
      }
    });
    
    // Check keywords count
    const keywordsMatch = siteConfigContent.match(/keywords:\s*\[([\s\S]*?)\]/);
    if (keywordsMatch) {
      const keywordsCount = keywordsMatch[1].split(',').length;
      if (keywordsCount >= 20) {
        logSuccess(`Site has ${keywordsCount} SEO keywords`);
      } else {
        logWarning(`Site has only ${keywordsCount} SEO keywords (recommended: 20+)`);
      }
    }
    
  } catch (error) {
    logError(`Failed to validate site config: ${error.message}`);
  }
}

function validateMetadata() {
  log('\n🏷️  Validating Metadata Files...', colors.cyan);
  
  const metadataFiles = [
    'app/(root)/metadata.ts',
    'app/(root)/about-us/metadata.ts',
    'app/(root)/products/metadata.ts',
    'app/(root)/contact/metadata.ts',
    'app/(root)/chiniot-furniture/metadata.ts'
  ];
  
  metadataFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      logSuccess(`Metadata file exists: ${file}`);
      
      const content = fs.readFileSync(filePath, 'utf8');
      
      // Check for required metadata fields
      if (content.includes('title:')) {
        logSuccess(`${file} has title`);
      } else {
        logError(`${file} missing title`);
      }
      
      if (content.includes('description:')) {
        logSuccess(`${file} has description`);
      } else {
        logError(`${file} missing description`);
      }
      
      if (content.includes('keywords:')) {
        logSuccess(`${file} has keywords`);
      } else {
        logWarning(`${file} missing keywords`);
      }
      
      if (content.includes('openGraph:')) {
        logSuccess(`${file} has Open Graph tags`);
      } else {
        logWarning(`${file} missing Open Graph tags`);
      }
      
    } else {
      logError(`Metadata file missing: ${file}`);
    }
  });
}

function validateSEOComponents() {
  log('\n🧩 Validating SEO Components...', colors.cyan);
  
  const seoComponents = [
    'components/SEO/EnhancedSEO.tsx',
    'components/SEO/PageSEO.tsx',
    'components/SEO/OptimizedImage.tsx',
    'components/SEO/CoreWebVitalsOptimizer.tsx',
    'components/SEO/AdvancedPerformanceOptimizer.tsx',
    'components/SEO/PerformanceMonitor.tsx',
    'components/SEO/LazyComponentLoader.tsx'
  ];
  
  seoComponents.forEach(component => {
    const componentPath = path.join(process.cwd(), component);
    if (fs.existsSync(componentPath)) {
      logSuccess(`SEO component exists: ${component}`);
    } else {
      logError(`SEO component missing: ${component}`);
    }
  });
}

function validateStructuredData() {
  log('\n📊 Validating Structured Data...', colors.cyan);
  
  const structuredDataFiles = [
    'components/SEO/OrganizationJsonLd.tsx',
    'components/SEO/LocalBusinessJsonLd.tsx',
    'components/SEO/ProductJsonLd.tsx',
    'components/SEO/BreadcrumbJsonLd.tsx',
    'components/SEO/FAQSchema.tsx'
  ];
  
  structuredDataFiles.forEach(file => {
    const filePath = path.join(process.cwd(), file);
    if (fs.existsSync(filePath)) {
      logSuccess(`Structured data component exists: ${file}`);
      
      const content = fs.readFileSync(filePath, 'utf8');
      if (content.includes('@context') && content.includes('schema.org')) {
        logSuccess(`${file} has valid schema.org markup`);
      } else {
        logError(`${file} missing schema.org markup`);
      }
    } else {
      logError(`Structured data component missing: ${file}`);
    }
  });
}

function validateSitemap() {
  log('\n🗺️  Validating Sitemap...', colors.cyan);
  
  const sitemapPath = path.join(process.cwd(), 'app', 'sitemap.ts');
  if (fs.existsSync(sitemapPath)) {
    logSuccess('Sitemap file exists');
    
    const content = fs.readFileSync(sitemapPath, 'utf8');
    
    // Check for required sitemap elements
    if (content.includes('MetadataRoute.Sitemap')) {
      logSuccess('Sitemap uses correct Next.js type');
    } else {
      logError('Sitemap missing correct type definition');
    }
    
    if (content.includes('changeFrequency')) {
      logSuccess('Sitemap includes changeFrequency');
    } else {
      logWarning('Sitemap missing changeFrequency');
    }
    
    if (content.includes('priority')) {
      logSuccess('Sitemap includes priority');
    } else {
      logWarning('Sitemap missing priority');
    }
    
  } else {
    logError('Sitemap file missing');
  }
}

function validateRobotsTxt() {
  log('\n🤖 Validating robots.txt...', colors.cyan);
  
  const robotsPath = path.join(process.cwd(), 'public', 'robots.txt');
  if (fs.existsSync(robotsPath)) {
    logSuccess('robots.txt exists');
    
    const content = fs.readFileSync(robotsPath, 'utf8');
    
    if (content.includes('User-agent: *')) {
      logSuccess('robots.txt has user-agent directive');
    } else {
      logError('robots.txt missing user-agent directive');
    }
    
    if (content.includes('Sitemap:')) {
      logSuccess('robots.txt includes sitemap reference');
    } else {
      logError('robots.txt missing sitemap reference');
    }
    
    if (content.includes('Disallow: /api/')) {
      logSuccess('robots.txt blocks API routes');
    } else {
      logWarning('robots.txt should block API routes');
    }
    
  } else {
    logError('robots.txt missing');
  }
}

function validateImages() {
  log('\n🖼️  Validating Image Optimization...', colors.cyan);
  
  // Check for required image files
  const requiredImages = [
    'public/favicon.ico',
    'public/favicon.svg',
    'public/apple-touch-icon.png',
    'public/og-image.jpg',
    'public/twitter-image.jpg'
  ];
  
  requiredImages.forEach(image => {
    const imagePath = path.join(process.cwd(), image);
    if (fs.existsSync(imagePath)) {
      logSuccess(`Required image exists: ${image}`);
    } else {
      logError(`Required image missing: ${image}`);
    }
  });
  
  // Check Next.js config for image optimization
  const nextConfigPath = path.join(process.cwd(), 'next.config.ts');
  if (fs.existsSync(nextConfigPath)) {
    const content = fs.readFileSync(nextConfigPath, 'utf8');
    
    if (content.includes('formats: ["image/avif", "image/webp"]')) {
      logSuccess('Next.js config includes modern image formats');
    } else {
      logWarning('Next.js config should include AVIF and WebP formats');
    }
    
    if (content.includes('deviceSizes:')) {
      logSuccess('Next.js config includes device sizes');
    } else {
      logWarning('Next.js config should include device sizes');
    }
  }
}

function validatePerformance() {
  log('\n⚡ Validating Performance Configuration...', colors.cyan);
  
  const nextConfigPath = path.join(process.cwd(), 'next.config.ts');
  if (fs.existsSync(nextConfigPath)) {
    const content = fs.readFileSync(nextConfigPath, 'utf8');
    
    if (content.includes('compress: true')) {
      logSuccess('Compression enabled');
    } else {
      logWarning('Compression should be enabled');
    }
    
    if (content.includes('poweredByHeader: false')) {
      logSuccess('X-Powered-By header disabled');
    } else {
      logWarning('X-Powered-By header should be disabled');
    }
    
    if (content.includes('optimizePackageImports:')) {
      logSuccess('Package import optimization enabled');
    } else {
      logWarning('Package import optimization should be enabled');
    }
  }
}

function generateReport() {
  log('\n📊 SEO Validation Report', colors.bright);
  log('='.repeat(50), colors.cyan);
  
  log(`✅ Passed: ${validationResults.passed}`, colors.green);
  log(`❌ Failed: ${validationResults.failed}`, colors.red);
  log(`⚠️  Warnings: ${validationResults.warnings}`, colors.yellow);
  
  const total = validationResults.passed + validationResults.failed + validationResults.warnings;
  const score = Math.round((validationResults.passed / total) * 100);
  
  log(`\n📈 SEO Score: ${score}%`, score >= 80 ? colors.green : score >= 60 ? colors.yellow : colors.red);
  
  if (validationResults.issues.length > 0) {
    log('\n🔍 Issues to Address:', colors.bright);
    validationResults.issues.forEach((issue, index) => {
      const icon = issue.type === 'error' ? '❌' : '⚠️';
      log(`${index + 1}. ${icon} ${issue.message}`);
    });
  }
  
  log('\n💡 Recommendations:', colors.bright);
  log('• Ensure all metadata files have complete information');
  log('• Add structured data to all product pages');
  log('• Optimize images with proper alt text');
  log('• Monitor Core Web Vitals regularly');
  log('• Keep SEO components updated');
  
  log('\n🎯 Next Steps:', colors.bright);
  log('• Run lighthouse audit: npm run performance:audit');
  log('• Analyze bundle size: npm run build:analyze');
  log('• Test on mobile devices');
  log('• Submit sitemap to search engines');
}

// Main execution
function main() {
  log('🚀 Starting SEO Validation for Chinioti Wooden Art', colors.bright);
  log('='.repeat(60), colors.cyan);
  
  validateSiteConfig();
  validateMetadata();
  validateSEOComponents();
  validateStructuredData();
  validateSitemap();
  validateRobotsTxt();
  validateImages();
  validatePerformance();
  
  generateReport();
}

// Run the validator
if (require.main === module) {
  main();
}

module.exports = {
  validateSiteConfig,
  validateMetadata,
  validateSEOComponents,
  validateStructuredData,
  validateSitemap,
  validateRobotsTxt,
  validateImages,
  validatePerformance,
  generateReport
};
