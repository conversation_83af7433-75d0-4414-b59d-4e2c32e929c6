'use client';
import React, { useState, useEffect } from 'react';
import { useCart } from '@/contexts/CartContext';
import { useOrders } from '@/contexts/OrderContext';
import { useAuth } from '@/contexts/AuthContext';
import { motion, AnimatePresence } from 'framer-motion';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { Trash2, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCurrency } from '@/contexts/CurrencyContext';
import ProtectedRoute from '@/components/auth/ProtectedRoute';
import WhatsAppOrderButton from '@/components/WhatsAppOrderButton';
import { WhatsAppOrderData } from '@/utils/whatsappOrder';
import { toast } from 'sonner';
import EnhancedCartItem from '@/components/cart/EnhancedCartItem';
import EnhancedCartSummary from '@/components/cart/EnhancedCartSummary';
import EnhancedEmptyCart from '@/components/cart/EnhancedEmptyCart';
import CartProgressIndicator from '@/components/cart/CartProgressIndicator';
import MobileCartLayout from '@/components/cart/MobileCartLayout';
import MobileCartItem from '@/components/cart/MobileCartItem';
import ConfirmationDialog from '@/components/ui/confirmation-dialog';
import LoadingState from '@/components/ui/loading-state';
import { ProductData } from '@/types';

const CartPage = () => {
  const { cart, updateQuantity, removeFromCart, clearCart } = useCart();
  const { createOrder } = useOrders();
  const { isAuthenticated, user } = useAuth();
  const { formatPrice, convertPrice, selectedCurrency } = useCurrency();
  const router = useRouter();
  const [showQuickOrderForm, setShowQuickOrderForm] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showClearCartDialog, setShowClearCartDialog] = useState(false);
  const [cartOperationLoading, setCartOperationLoading] = useState<string | null>(null);
  const [recentlyViewed, setRecentlyViewed] = useState<ProductData[]>([]);
  const [recommendedProducts, setRecommendedProducts] = useState<ProductData[]>([]);
  const [quickOrderData, setQuickOrderData] = useState({
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    address: '',
    city: '',
    postalCode: '',
  });

  // Load recently viewed and recommended products
  useEffect(() => {
    const loadRecentlyViewed = () => {
      try {
        const stored = localStorage.getItem('recentlyViewed');
        if (stored) {
          const products = JSON.parse(stored);
          setRecentlyViewed(products.slice(0, 4));
        }
      } catch (error) {
        console.error('Failed to load recently viewed products:', error);
      }
    };

    const loadRecommendedProducts = async () => {
      try {
        // In a real app, this would be an API call based on user preferences
        // For now, we'll simulate with some sample data
        const sampleProducts: ProductData[] = [];
        setRecommendedProducts(sampleProducts);
      } catch (error) {
        console.error('Failed to load recommended products:', error);
      }
    };

    loadRecentlyViewed();
    loadRecommendedProducts();
  }, []);

  // Enhanced cart operations with loading states
  const handleUpdateQuantity = async (productId: string, quantity: number) => {
    setCartOperationLoading(productId);
    try {
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API delay
      updateQuantity(productId, quantity);
    } catch (error) {
      toast.error('Failed to update quantity');
      throw error;
    } finally {
      setCartOperationLoading(null);
    }
  };

  const handleRemoveItem = async (productId: string) => {
    setCartOperationLoading(productId);
    try {
      await new Promise(resolve => setTimeout(resolve, 300)); // Simulate API delay
      removeFromCart(productId);
    } catch (error) {
      toast.error('Failed to remove item');
      throw error;
    } finally {
      setCartOperationLoading(null);
    }
  };

  const handleClearCart = async () => {
    setIsLoading(true);
    try {
      await new Promise(resolve => setTimeout(resolve, 500)); // Simulate API delay
      clearCart();
      setShowClearCartDialog(false);
      toast.success('Cart cleared successfully');
    } catch (error) {
      toast.error('Failed to clear cart');
    } finally {
      setIsLoading(false);
    }
  };

  // Calculate discounted price - same logic as product detail page
  const getDiscountedPrice = (price: number, discount: string) => {
    return discount ? Math.round(price - (price * parseInt(discount)) / 100) : price;
  };

  const handleQuickOrderInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setQuickOrderData(prev => ({ ...prev, [name]: value }));
  };

  const handleQuickWhatsAppOrder = async (): Promise<WhatsAppOrderData> => {
    // Validate required fields
    const requiredFields = ['firstName', 'lastName', 'phone', 'address', 'city'];
    const missingFields = requiredFields.filter(field => !quickOrderData[field as keyof typeof quickOrderData]);

    if (missingFields.length > 0) {
      toast.error('Please fill in all required fields');
      throw new Error('Missing required fields');
    }

    if (!isAuthenticated || !user?.id) {
      toast.error('You must be logged in to place an order');
      throw new Error('User not authenticated');
    }

    setIsLoading(true);

    try {
      // Create order data
      const orderData = {
        items: cart.items,
        total: cart.total,
        subtotal: cart.subtotal,
        discount: cart.discount,
        customer: {
          firstName: quickOrderData.firstName,
          lastName: quickOrderData.lastName,
          email: quickOrderData.email,
          phone: quickOrderData.phone,
        },
        shipping: {
          address: quickOrderData.address,
          city: quickOrderData.city,
          postalCode: quickOrderData.postalCode,
        },
        paymentMethod: "cash" as "cash" | "bank", // Default to cash for WhatsApp orders
        status: "pending" as const,
      };

      // Create order in database
      const createdOrder = await createOrder(orderData);

      // Prepare WhatsApp order data with the created order
      const whatsAppOrderData = {
        order: createdOrder,
        customerDetails: {
          firstName: quickOrderData.firstName,
          lastName: quickOrderData.lastName,
          email: quickOrderData.email,
          phone: quickOrderData.phone,
          address: quickOrderData.address,
          city: quickOrderData.city,
          postalCode: quickOrderData.postalCode,
        },
        formatPrice, // Pass the currency formatter from context
        selectedCurrency, // Pass the selected currency
      };

      // Clear cart after successful order creation
      clearCart();
      
      toast.success("Order created successfully! Redirecting to WhatsApp...");
      
      // Close the cart page by navigating to orders page after a short delay
      setTimeout(() => {
        router.push("/orders");
      }, 2000);
      
      // Return the WhatsApp order data for the button to handle
      return whatsAppOrderData;
    } catch (error) {
      console.error("Error creating order:", error);
      toast.error("Failed to create order. Please try again.");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-gray-50">
        {/* Header Section */}
        <div className="bg-white border-b border-gray-200">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            {/* Back Button */}
            <div className="flex items-center gap-4 mb-4">
              <Link href="/products">
                <Button variant="ghost" size="sm" className="gap-2">
                  <ArrowLeft size={16} />
                  Continue Shopping
                </Button>
              </Link>
            </div>

            {/* Page Title */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Shopping Cart</h1>
                <p className="text-gray-600 mt-1">
                  Review your items and proceed to checkout
                </p>
              </div>

              {/* Clear Cart Button */}
              {cart.items.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowClearCartDialog(true)}
                  disabled={isLoading}
                  className="text-red-600 border-red-200 hover:bg-red-50 hover:border-red-300"
                >
                  <Trash2 size={16} className="mr-2" />
                  Clear Cart
                </Button>
              )}
            </div>

            {/* Progress Indicator */}
            {cart.items.length > 0 && (
              <div className="mt-6">
                <CartProgressIndicator
                  currentStep="cart"
                  showLabels={false}
                  className="max-w-md"
                />
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {cart.items.length === 0 ? (
            <EnhancedEmptyCart
              recentlyViewed={recentlyViewed}
              recommendedProducts={recommendedProducts}
            />
          ) : (
            <>
              {/* Desktop Layout */}
              <div className="hidden lg:grid lg:grid-cols-3 gap-8">
                {/* Cart Items */}
                <div className="lg:col-span-2 space-y-4">
                  {/* Cart Items Header */}
                  <div className="bg-white rounded-lg shadow-sm p-4">
                    <h2 className="text-lg font-semibold text-gray-900">
                      Cart Items ({cart.totalItems})
                    </h2>
                    <p className="text-sm text-gray-600 mt-1">
                      Review and modify your selected items
                    </p>
                  </div>

                  {/* Cart Items List */}
                  <AnimatePresence mode="popLayout">
                    {cart.items.map((item) => (
                      <EnhancedCartItem
                        key={item.product.id}
                        item={item}
                        onUpdateQuantity={handleUpdateQuantity}
                        onRemove={handleRemoveItem}
                        loading={cartOperationLoading === item.product.id}
                      />
                    ))}
                  </AnimatePresence>
                </div>

                <div className="lg:col-span-1">
                  <div className="sticky top-4 space-y-4">
                    <EnhancedCartSummary
                      cart={cart}
                      onCheckout={() => router.push('/checkout')}
                      onQuickOrder={() => setShowQuickOrderForm(!showQuickOrderForm)}
                      loading={isLoading}
                      showQuickOrderForm={showQuickOrderForm}
                    />

                    {/* Quick Order Form */}
                    {showQuickOrderForm && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: 'auto' }}
                        exit={{ opacity: 0, height: 0 }}
                        className="bg-white rounded-lg shadow-sm p-4"
                      >
                        <h3 className="text-lg font-semibold mb-4">Quick Order Details</h3>
                        <div className="space-y-3">
                          <div className="grid grid-cols-2 gap-2">
                            <input
                              type="text"
                              name="firstName"
                              placeholder="First Name *"
                              value={quickOrderData.firstName}
                              onChange={handleQuickOrderInputChange}
                              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                              required
                            />
                            <input
                              type="text"
                              name="lastName"
                              placeholder="Last Name *"
                              value={quickOrderData.lastName}
                              onChange={handleQuickOrderInputChange}
                              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                              required
                            />
                          </div>

                          <input
                            type="email"
                            name="email"
                            placeholder="Email Address"
                            value={quickOrderData.email}
                            onChange={handleQuickOrderInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          />

                          <input
                            type="tel"
                            name="phone"
                            placeholder="Phone Number *"
                            value={quickOrderData.phone}
                            onChange={handleQuickOrderInputChange}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                            required
                          />

                          <textarea
                            name="address"
                            placeholder="Delivery Address *"
                            value={quickOrderData.address}
                            onChange={handleQuickOrderInputChange}
                            rows={2}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                            required
                          />

                          <div className="grid grid-cols-2 gap-2">
                            <input
                              type="text"
                              name="city"
                              placeholder="City *"
                              value={quickOrderData.city}
                              onChange={handleQuickOrderInputChange}
                              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                              required
                            />
                            <input
                              type="text"
                              name="postalCode"
                              placeholder="Postal Code"
                              value={quickOrderData.postalCode}
                              onChange={handleQuickOrderInputChange}
                              className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                            />
                          </div>

                          <WhatsAppOrderButton
                            onOrderCreate={handleQuickWhatsAppOrder}
                            className="w-full"
                            disabled={isLoading || !quickOrderData.firstName || !quickOrderData.lastName || !quickOrderData.phone || !quickOrderData.address || !quickOrderData.city}
                          >
                            {isLoading ? 'Creating Order...' : 'Send Order via WhatsApp'}
                          </WhatsAppOrderButton>
                        </div>
                      </motion.div>
                    )}
                  </div>
                </div>
              </div>

              {/* Mobile Layout */}
              <MobileCartLayout
                cart={cart}
                summaryComponent={
                  <EnhancedCartSummary
                    cart={cart}
                    onCheckout={() => router.push('/checkout')}
                    onQuickOrder={() => setShowQuickOrderForm(!showQuickOrderForm)}
                    loading={isLoading}
                    showQuickOrderForm={showQuickOrderForm}
                  />
                }
              >
                <AnimatePresence mode="popLayout">
                  {cart.items.map((item) => (
                    <MobileCartItem
                      key={item.product.id}
                      item={item}
                      onUpdateQuantity={handleUpdateQuantity}
                      onRemove={handleRemoveItem}
                      loading={cartOperationLoading === item.product.id}
                    />
                  ))}
                </AnimatePresence>
              </MobileCartLayout>
            </>
          )}

            {/* Order Summary */}
            <div className="lg:col-span-1">
              <div className="sticky top-4 space-y-4">
                <EnhancedCartSummary
                  cart={cart}
                  onCheckout={() => router.push('/checkout')}
                  onQuickOrder={() => setShowQuickOrderForm(!showQuickOrderForm)}
                  loading={isLoading}
                  showQuickOrderForm={showQuickOrderForm}
                />

                {/* Quick Order Form */}
                {showQuickOrderForm && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    className="bg-white rounded-lg shadow-sm p-4"
                  >
                    <h3 className="text-lg font-semibold mb-4">Quick Order Details</h3>
                    <div className="space-y-3">
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          name="firstName"
                          placeholder="First Name *"
                          value={quickOrderData.firstName}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                        <input
                          type="text"
                          name="lastName"
                          placeholder="Last Name *"
                          value={quickOrderData.lastName}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                      </div>

                      <input
                        type="email"
                        name="email"
                        placeholder="Email Address"
                        value={quickOrderData.email}
                        onChange={handleQuickOrderInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                      />

                      <input
                        type="tel"
                        name="phone"
                        placeholder="Phone Number *"
                        value={quickOrderData.phone}
                        onChange={handleQuickOrderInputChange}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        required
                      />
                      
                      <textarea
                        name="address"
                        placeholder="Delivery Address *"
                        value={quickOrderData.address}
                        onChange={handleQuickOrderInputChange}
                        rows={2}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        required
                      />
                      
                      <div className="grid grid-cols-2 gap-2">
                        <input
                          type="text"
                          name="city"
                          placeholder="City *"
                          value={quickOrderData.city}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                          required
                        />
                        <input
                          type="text"
                          name="postalCode"
                          placeholder="Postal Code"
                          value={quickOrderData.postalCode}
                          onChange={handleQuickOrderInputChange}
                          className="px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-accent/50"
                        />
                      </div>

                      <WhatsAppOrderButton
                        onOrderCreate={handleQuickWhatsAppOrder}
                        className="w-full"
                        disabled={isLoading || !quickOrderData.firstName || !quickOrderData.lastName || !quickOrderData.phone || !quickOrderData.address || !quickOrderData.city}
                      >
                        {isLoading ? 'Creating Order...' : 'Send Order via WhatsApp'}
                      </WhatsAppOrderButton>
                    </div>
                  </motion.div>
                )}
              </div>
            </div>
          </div>
        
        

        {/* Confirmation Dialogs */}
        <ConfirmationDialog
          isOpen={showClearCartDialog}
          onClose={() => setShowClearCartDialog(false)}
          onConfirm={handleClearCart}
          title="Clear Cart"
          description={`Are you sure you want to remove all ${cart.totalItems} item${cart.totalItems !== 1 ? 's' : ''} from your cart? This action cannot be undone.`}
          confirmText="Clear Cart"
          cancelText="Keep Items"
          variant="danger"
          loading={isLoading}
        />
      </div>
    </ProtectedRoute>
  );
};

export default CartPage;