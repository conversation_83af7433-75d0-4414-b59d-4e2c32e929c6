"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { ShoppingBag, Truck, Shield, CreditCard, Gift, Info } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useCurrency } from '@/contexts/CurrencyContext';
import { Cart } from '@/types/cart';
import { cn } from '@/lib/utils';

interface EnhancedCartSummaryProps {
  cart: Cart;
  onCheckout: () => void;
  onQuickOrder: () => void;
  loading?: boolean;
  showQuickOrderForm?: boolean;
  className?: string;
}

const EnhancedCartSummary: React.FC<EnhancedCartSummaryProps> = ({
  cart,
  onCheckout,
  onQuickOrder,
  loading = false,
  showQuickOrderForm = false,
  className = '',
}) => {
  const { formatPrice } = useCurrency();
  const [expandedSection, setExpandedSection] = useState<string | null>(null);

  const shippingThreshold = 5000; // Free shipping threshold
  const remainingForFreeShipping = Math.max(0, shippingThreshold - cart.total);
  const hasEarnedFreeShipping = remainingForFreeShipping === 0;

  const summaryItems = [
    {
      label: 'Subtotal',
      value: formatPrice(cart.subtotal),
      description: `${cart.totalItems} item${cart.totalItems !== 1 ? 's' : ''}`,
    },
    ...(cart.discount > 0 ? [{
      label: 'Discount',
      value: `-${formatPrice(cart.discount)}`,
      description: 'You saved',
      className: 'text-green-600',
    }] : []),
  
  ];

  

  return (
    <div className={cn('bg-white rounded-lg shadow-sm border border-gray-100', className)}>
      {/* Header */}
      <div className="p-4 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <ShoppingBag size={20} className="text-accent" />
          <h2 className="text-lg font-semibold text-gray-900">Order Summary</h2>
        </div>
      </div>

      {/* Summary Items */}
      <div className="p-4 space-y-3">
        {summaryItems.map((item, index) => (
          <motion.div
            key={item.label}
            className="flex justify-between items-start"
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <div className="flex-1">
              <div className="font-medium text-gray-900">{item.label}</div>
              {item.description && (
                <div className="text-sm text-gray-500">{item.description}</div>
              )}
            </div>
            <div className={cn('font-semibold text-right', item.className)}>
              {item.value}
            </div>
          </motion.div>
        ))}

        {/* Free Shipping Progress */}
        {!hasEarnedFreeShipping && (
          <motion.div
            className="bg-gray-50 rounded-lg p-3 mt-4"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
          >
            {/* <div className="flex items-center gap-2 mb-2">
              <Truck size={16} className="text-accent" />
              <span className="text-sm font-medium text-gray-700">
                Free Shipping Progress
              </span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
              <div
                className="bg-accent h-2 rounded-full transition-all duration-500"
                style={{ width: `${Math.min(100, (cart.total / shippingThreshold) * 100)}%` }}
              />
            </div> */}
            
          </motion.div>
        )}

        {/* Total */}
        <div className="border-t border-gray-100 pt-3 mt-4">
          <div className="flex justify-between items-center">
            <span className="text-lg font-semibold text-gray-900">Total</span>
            <span className="text-xl font-bold text-accent">
              {formatPrice(cart.total)}
            </span>
          </div>
          {cart.discount > 0 && (
            <div className="text-sm text-green-600 text-right mt-1">
              Total savings: {formatPrice(cart.discount)}
            </div>
          )}
        </div>
      </div>

      {/* Action Buttons */}
      <div className="p-4 border-t border-gray-100 space-y-3">
        {/* Primary Checkout Button */}
        <Button
          variant="default"
          size="lg"
          onClick={onCheckout}
          disabled={loading || cart.totalItems === 0}
          className="w-full font-semibold"
        >
          <CreditCard size={18} className="mr-2" />
          {loading ? 'Processing...' : 'Proceed to Checkout'}
        </Button>

        

        {/* Trust Indicators */}
        <div className="grid grid-cols-2 gap-2 mt-4">
          <div className="flex items-center gap-2 text-xs text-gray-600 bg-gray-50 rounded-lg p-2">
            <Shield size={14} className="text-green-600" />
            <span>Secure Checkout</span>
          </div>
          <div className="flex items-center gap-2 text-xs text-gray-600 bg-gray-50 rounded-lg p-2">
            <Truck size={14} className="text-blue-600" />
            <span>Fast Delivery</span>
          </div>
        </div>
      </div>

    </div>
  );
};

export default EnhancedCartSummary;
